import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import paramiko
import stat
# from ml_predict_3h_2buy import MarketPredictor, FEATURE_COLUMNS, preprocess_data

def upload_file_to_ubuntu(local_file_path, remote_dir_path, server_ip, username, password):
    try:
        # 建立SSH传输连接
        transport = paramiko.Transport((server_ip, 22))
        transport.connect(username=username, password=password)

        # 创建SFTP客户端
        sftp = paramiko.SFTPClient.from_transport(transport)

        # 检查远程目录是否存在,不存在则创建
        try:
            sftp.stat(remote_dir_path)
        except IOError:
            sftp.mkdir(remote_dir_path)

        # 获取本地文件名
        local_filename = os.path.basename(local_file_path)
        
        # 构建远程文件完整路径
        remote_file_path = os.path.join(remote_dir_path, local_filename)

        # 上传文件
        sftp.put(local_file_path, remote_file_path)
        print(f"文件 '{local_file_path}' 已成功上传到远程服务器 '{remote_file_path}'")

        # 关闭连接
        sftp.close()
        transport.close()

    except Exception as e:
        print(f"上传文件时出错: {e}")

remote_dir_path = "/var/www/html/files/"
ubuntu_server_ip = "*************"
ubuntu_username = "user1/root"
ubuntu_password = "@uSJVBqSCP2E"

token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        # requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)
# 定义目录路径
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 定义日期范围
# start_date = "20220101"
# end_date = "20250110"
end_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")
start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=70)).strftime("%Y%m%d")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\20di_today.csv'

# 1. 优化数据读取
def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice', 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }
    
    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"), 
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"), 
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"), 
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]), 
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

# 2. 优化数据处理逻辑
def process_stock_data(group):
    """处理单个股票的数据"""
    if len(group) < 40:  # 修改为40天的最小数据要求
        print(f"警告: {group.iloc[0]['secShortName']} 数据天数不足40天，跳过处理")
        return []
    
    results = []
    group = group.reset_index(drop=True)
    
    print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    print(f"数据天数: {len(group)}")
    
    # 计算40天滚动最低价（包含当天）
    rolling_min = group['lowestPrice'].rolling(window=40, min_periods=40).min()
    
    # 打印关键数据点
    found_patterns = 0
    for i in range(39, len(group)-1):  # 从第40天开始，确保有足够的历史数据
        current_min = rolling_min[i-1]  # 当前40天的最低价
        
        if group.loc[i, "lowestPrice"] <= current_min:
            print(f"\n发现潜在模式 - 日期索引: {i}")
            print(f"最低价: {group.loc[i, 'lowestPrice']:.2f}")
            print(f"40日最低: {current_min:.2f}")
            
            if group.loc[i+1, "chgPct"] > -0.9:
                print(f"次日涨幅: {group.loc[i+1, 'chgPct']:.2f}%")
                               
                if group.loc[i, "chgPct"] <= 0.03 and rolling_min[i-1] > 0 and group.loc[i, "lowestPrice"] > 0 and group.loc[i, "closePrice"] > group.loc[i, "limitDownPrice"]:
                    found_patterns += 1
                    results.append({
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "前40日最低价": rolling_min[i-1],
                        "i_涨跌幅": group.loc[i, "chgPct"],
                        "i1_反弹日期": group.loc[i+1, "date"],
                        "i1_涨跌幅_大于3%": group.loc[i+1, "chgPct"],
                        # "开盘买_两日连续上涨": group.loc[i, "chgPct"] > 0.01 and group.loc[i+1, "chgPct"] >= 0.02,
                        # "turnoverRate_i": group.loc[i, "turnoverRate"],
                        # "turnoverRate_i1": group.loc[i+1, "turnoverRate"],
                        "i1_量比_小于0": group.loc[i+1, "turnoverVol"]/group.loc[i, "turnoverVol"]-1,
                        "i1_量比VEMA5_小于0": group.loc[i+1, "turnoverVol"]/group.loc[i, "VEMA5"]-1,
                        "反弹日是否创新低": group.loc[i+1, "lowestPrice"] < group.loc[i, "lowestPrice"]
                    })
    
    print(f"找到符合条件的模式数量: {found_patterns}")
    return results

# 3. 主程序优化
print(f"开始处理数据: {start_date} 到 {end_date}")

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) 
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) & 
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) & 
    (~all_data['secShortName'].str.contains('B|ST')) & 
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

results_df = pd.DataFrame(all_results)
if not results_df.empty:
    # 获取最大的反弹日期
    max_date = results_df['i1_反弹日期'].max()
    # 只保留最大日期的数据
    results_df = results_df[results_df['i1_反弹日期'] == max_date]

    # 按照 i1_涨跌幅 列降序排序
    results_df = results_df.sort_values(by='i1_涨跌幅_大于3%', ascending=False)

    # 合并版块信息
    zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
    results_df = pd.merge(results_df, zhangting_all, how='left', on=['secID'])
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    results_df = pd.merge(results_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    results_df = pd.merge(results_df, stock_board_concept_name_em_merged, how='left', on=['secID'])
    
    # 保存最终结果
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    # upload_file_to_ubuntu(output_file, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
    print("\n最终结果统计:")
    print(f"找到最后日期的总模式数量: {len(results_df)}")
    print(f"结果已保存到: {output_file}")
