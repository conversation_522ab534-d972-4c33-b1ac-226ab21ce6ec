
import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import os
import numpy as np

def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice', 
                 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate', 'openPrice'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }

    
    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"), 
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"), 
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"), 
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]), 
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

def process_stock_data(group):
    """处理单个股票的数据"""
    if len(group) < 15:  # 修改为至少需要15天数据
        return []
    
    results = []
    group = group.reset_index(drop=True)
    
    print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    print(f"数据天数: {len(group)}")
    
    # 计算15天滚动最低价和最高价
    rolling_min = group['lowestPrice'].rolling(window=15, min_periods=15).min()
    rolling_max = group['highestPrice'].rolling(window=15, min_periods=15).max()
    
    found_patterns = 0
    for i in range(14, len(group)-2):  # 从第15天开始，确保有足够的历史数据
        current_min = rolling_min[i]  # 当前15天的最低价
        current_max = rolling_max[i]  # 当前15天的最高价
        
        if group.loc[i, "lowestPrice"] <= current_min: 
            # 基础条件检查
            if (i+3 < len(group) and 
                rolling_min[i] > 0 and 
                group.loc[i, "lowestPrice"] > 0 and 
                group.loc[i+3, "highestPrice"] > 0):
                
                # 第二天放量上涨检查（成交量是第一天的2倍，且不涨停，且最高价大于等于前15天最高价）
                if (group.loc[i+1, "turnoverVol"] >= 2 * group.loc[i, "turnoverVol"] and 
                    group.loc[i+1, "chgPct"] > 0 and 
                    group.loc[i+1, "closePrice"] < group.loc[i+1, "limitUpPrice"]and 
                    group.loc[i+1, "closePrice"] > group.loc[i+1, "openPrice"]):  # 新增条件：最高价大于等于前15天最高价 and group.loc[i+1, "highestPrice"] >= current_max)
                    
                    result = {
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "前15日最低价": rolling_min[i],
                        "前15日最高价": rolling_max[i],  # 新增字段
                        "i_最低价": group.loc[i, "lowestPrice"],
                        "i_涨跌幅": group.loc[i, "chgPct"],
                        "i1_反弹日期": group.loc[i+1, "date"],
                        "i1_涨跌幅": group.loc[i+1, "chgPct"],
                        "i1_成交量": group.loc[i+1, "turnoverVol"],
                        "i1_成交量比": group.loc[i+1, "turnoverVol"] / group.loc[i, "turnoverVol"],
                        "i1_最高价": group.loc[i+1, "highestPrice"],  # 新增字段
                        "i1_最高_收盘比例": group.loc[i+1, "highestPrice"]/group.loc[i+1, "closePrice"]-1,
                        "i1_closePrice": group.loc[i+1, "closePrice"],
                        "i2_openPrice": group.loc[i+2, "openPrice"],  # 第三天开盘价（买入价）
                        "i2_closePrice": group.loc[i+2, "closePrice"],
                        "i3_highestPrice": group.loc[i+3, "highestPrice"],  # 第四天最高价（卖出价）
                        "i3_preClosePrice": group.loc[i+3, "preClosePrice"]
                    }
                    
                    # 添加第二天(i+1)相比第一天(i)的factors列比值
                    factor_cols = ['MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 
                                  'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 
                                  'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 
                                  'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 
                                  'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 
                                  'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 
                                  'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'UOS', 
                                  'MA10RegressCoeff12', 'MA10RegressCoeff6']
                    
                    for col in factor_cols:
                        if col in group.columns:
                            current_value = group.loc[i+1, col]  # 第二天的值
                            prev_value = group.loc[i, col]       # 第一天的值
                            if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                ratio = current_value / prev_value
                                # result[f"i2_i1_{col}_ratio"] = ratio
                    
                    found_patterns += 1
                    results.append(result)
    
    print(f"找到符合条件的模式数量: {found_patterns}")
    return results

def calculate_statistics(results_df):
    """计算更详细的统计指标"""
    # 创建统计结果字典
    statistics_results = {}
    
    # 1. 收益率统计
    TRANSACTION_COST = 0.003
    # 修改收益率计算，使用第三天开盘买入，第四天最高价卖出
    results_df['3H_2buy'] = round((results_df['i3_highestPrice']/results_df['i2_openPrice']-1)*100, 2)
    results_df['成本后收益率'] = results_df['3H_2buy'] - TRANSACTION_COST * 200
    results_df['收盘价收益率'] = round((results_df['i3_preClosePrice']/results_df['i2_openPrice']-1)*100, 2)
    results_df['成本后收盘收益率'] = results_df['收盘价收益率'] - TRANSACTION_COST * 200
    
    statistics_results['收益率统计'] = {
        '平均最高价收益率': f"{results_df['3H_2buy'].mean():.2f}%",
        '平均收盘价收益率': f"{results_df['收盘价收益率'].mean():.2f}%",
        '成本后平均最高收益': f"{results_df['成本后收益率'].mean():.2f}%",
        '成本后平均收盘收益': f"{results_df['成本后收盘收益率'].mean():.2f}%",
        '标准差': f"{results_df['3H_2buy'].std():.2f}%",
        '最大收益': f"{results_df['3H_2buy'].max():.2f}%",
        '最小收益': f"{results_df['3H_2buy'].min():.2f}%",
    }
    
    # 2. 胜率统计
    statistics_results['胜率统计'] = {
        '最高价收益>0': f"{(results_df['3H_2buy'] > 0).mean() * 100:.2f}%",
        '最高价收益>1': f"{(results_df['3H_2buy'] > 1).mean() * 100:.2f}%",
        '最高价收益>3': f"{(results_df['3H_2buy'] > 3).mean() * 100:.2f}%",
        '收盘价收益>0': f"{(results_df['收盘价收益率'] > 0).mean() * 100:.2f}%",
        '成本后收益>0': f"{(results_df['成本后收益率'] > 0).mean() * 100:.2f}%",
    }
    
    # 3. 夏普比率
    risk_free_rate = 0.03
    daily_returns = results_df['3H_2buy'] / 100
    excess_returns = daily_returns - risk_free_rate/252
    sharpe_ratio = np.sqrt(252) * excess_returns.mean() / excess_returns.std()
    statistics_results['风险指标'] = {
        '夏普比率': f"{sharpe_ratio:.2f}"
    }
    
    # 4. 流动性分析
    results_df['成交额'] = results_df['i2_closePrice'] * results_df['i1_成交量']
    statistics_results['流动性分析'] = {
        '平均成交额(万)': f"{results_df['成交额'].mean() / 10000:.2f}",
        '最小成交额(万)': f"{results_df['成交额'].min() / 10000:.2f}",
        '最大成交额(万)': f"{results_df['成交额'].max() / 10000:.2f}",
    }
    
    # 5. 滑点影响分析
    slippage_analysis = {}
    for slippage in [0.001, 0.002, 0.003]:
        adj_return = results_df['3H_2buy'] - slippage * 200
        slippage_analysis[f'滑点{slippage*100}‰'] = f"{adj_return.mean():.2f}%"
    statistics_results['滑点影响分析'] = slippage_analysis
    
    # 6. 修改后的回撤分析
    # 每笔交易的回撤计算
    results_df['单笔回撤'] = results_df.apply(lambda x: min(
        # 计算买入后的最大亏损比例
        0,  # 因为我们只考虑第四天最高价卖出，所以这里简化处理
        (x['i3_preClosePrice'] / x['i2_openPrice'] - 1) * 100  # 次日收盘价回撤
    ), axis=1)
    
    # 按交易日期分组计算每日平均收益
    daily_returns = results_df.groupby('i1_反弹日期')['3H_2buy'].mean()
    
    statistics_results['回撤分析'] = {
        '单笔最大回撤': f"{results_df['单笔回撤'].min():.2f}%",  # 所有交易中最大的单笔亏损
        '平均回撤': f"{results_df['单笔回撤'].mean():.2f}%",     # 平均回撤幅度
        '单日最大回撤': f"{daily_returns.min():.2f}%",          # 单个交易日的最大平均亏损
        '回撤>3%占比': f"{(results_df['单笔回撤'] < -3).mean() * 100:.2f}%"  # 大幅回撤的比例
    }
    
    # 7. 交易成本敏感性分析
    cost_analysis = {}
    for cost in [0.001, 0.002, 0.003, 0.004]:
        adj_return = results_df['3H_2buy'] - cost * 200
        win_rate = (adj_return > 0).mean() * 100
        cost_analysis[f'成本{cost*100}‰'] = {
            '平均收益率': f"{adj_return.mean():.2f}%",
            '胜率': f"{win_rate:.2f}%"
        }
    statistics_results['交易成本分析'] = cost_analysis
    
    # 8. 基础统计信息
    statistics_results['基础信息'] = {
        '回测开始日期': start_date,
        '回测结束日期': end_date,
        '总交易次数': len(results_df),
        '不同交易日数量': len(results_df['i1_反弹日期'].unique()),
        '平均每日交易次数': f"{len(results_df)/len(results_df['i1_反弹日期'].unique()):.2f}"
    }
    
    # 保存统计结果到文件
    stats_file = output_file.replace('.csv', '_统计结果.txt')
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write(f"=== 策略回测统计结果 ===\n")
        f.write(f"回测期间: {start_date} 至 {end_date}\n\n")
        
        for category, stats in statistics_results.items():
            f.write(f"\n=== {category} ===\n")
            if isinstance(stats, dict):
                for key, value in stats.items():
                    if isinstance(value, dict):
                        f.write(f"\n{key}:\n")
                        for sub_key, sub_value in value.items():
                            f.write(f"  {sub_key}: {sub_value}\n")
                    else:
                        f.write(f"{key}: {value}\n")
            else:
                f.write(f"{stats}\n")
    
    print(f"\n统计结果已保存到: {stats_file}")
    
    # 同时打印到控制台
    print("\n=== 详细统计分析 ===")
    for category, stats in statistics_results.items():
        print(f"\n{category}:")
        if isinstance(stats, dict):
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"\n{key}:")
                    for sub_key, sub_value in value.items():
                        print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")
        else:
            print(stats)
    
    return results_df

# 2. 然后是全局变量定义
token = '44055711dfeb4124a8df9531f8dd2f59'
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 资金流向数据目录
fund_flow_dir = r'stock_fund_flow_data'

# 检查资金流向数据目录
if not os.path.exists(fund_flow_dir):
    print(f"警告：资金流向数据目录不存在: {fund_flow_dir}")
    print("将跳过资金流向数据的加载")

# 3. 主程序代码
start_date = "20250122"
end_date = "20250724"
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\coding\\gupiao_huice\\15di_fantan_' + start_date + end_date + '.csv'  # 修改输出文件名

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) 
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) & 
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) & 
    (~all_data['secShortName'].str.contains('B|ST')) & 
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n最终结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

results_df = pd.DataFrame(all_results)
if not results_df.empty:
    # 计算收益率，直接使用第三天开盘价买入，第四天最高价卖出
    # results_df['3H_2buyo'] = round((results_df['i2_closePrice']/results_df['i2_openPrice']-1)*100, 2)
    results_df['3H_2buy'] = round((results_df['i3_highestPrice']/results_df['i2_openPrice']-1)*100, 2)
    
    # 进行详细统计分析
    results_df = calculate_statistics(results_df)
    
    # 保存结果
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到: {output_file}")
